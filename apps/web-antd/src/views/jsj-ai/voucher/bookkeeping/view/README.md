# 凭证查看页面 - 凭证类型分类功能

## 功能概述

为了符合会计检查逻辑，我们为凭证查看页面添加了凭证类型分类功能，支持按照不同的原始凭证类型进行筛选和查看。

## 主要改进

### 1. 凭证类型筛选器 (VoucherTypeFilter.vue)

**功能特点：**
- 支持按凭证类型筛选：全部、进项发票、销项发票、银行回单、工资单
- 实时显示各类型凭证的数量统计
- 支持单选和多选模式
- 不同类型使用不同的颜色和图标标识

**使用方式：**
```vue
<VoucherTypeFilter
  :voucher-list="voucherList"
  :selected-types="selectedVoucherTypes"
  @change="handleVoucherTypeChange"
/>
```

### 2. 凭证类型视觉标识

**改进内容：**
- 为不同类型的凭证添加了颜色标识
- 进项发票：绿色 (#52c41a)
- 销项发票：橙色 (#fa8c16)  
- 银行回单：蓝色 (#1890ff)
- 工资单：紫色 (#722ed1)

### 3. 智能过滤逻辑

**功能实现：**
- 根据选择的凭证类型实时过滤显示列表
- 支持"全部"选项显示所有凭证
- 筛选变化时自动重置选择状态

## 会计检查逻辑优化

### 问题解决

**原问题：**
- 发票、回单、工资单等混放在一起，不符合会计检查逻辑
- 缺乏有效的分类筛选功能
- 会计人员难以按类型进行专项检查

**解决方案：**
1. **分类筛选**：按照会计业务类型进行分类
2. **视觉区分**：不同类型使用不同颜色标识
3. **统计信息**：显示各类型凭证数量，便于工作量评估
4. **工作流程优化**：支持按类型进行专项编辑

### 会计工作流程改进

1. **按类型检查**：会计人员可以先检查某一类型的所有凭证
2. **工作量评估**：通过统计信息了解各类型凭证的分布
3. **专项编辑**：针对特定类型进行集中编辑
4. **效率提升**：减少在不同类型凭证间切换的时间

## 技术实现

### 核心组件

1. **VoucherTypeFilter.vue**：凭证类型筛选组件
2. **VirtualVoucherList.vue**：虚拟凭证列表（已优化）
3. **index.vue**：主页面（已集成筛选功能）

### 关键功能

```typescript
// 过滤逻辑
const filteredVoucherList = computed(() => {
  if (selectedVoucherTypes.value.includes('all')) {
    return voucherList.value;
  }
  
  return voucherList.value.filter((voucher) => {
    return selectedVoucherTypes.value.includes(voucher.source_type);
  });
});

// 类型统计
function getVoucherTypeStats() {
  const stats: Record<string, number> = {};
  
  props.voucherList.forEach((voucher) => {
    const sourceType = voucher.source_type || '未知';
    stats[sourceType] = (stats[sourceType] || 0) + 1;
  });
  
  return stats;
}
```

## 使用说明

1. **筛选凭证**：点击筛选器中的类型按钮进行筛选
2. **查看统计**：筛选器显示各类型凭证的数量
3. **多选支持**：可以同时选择多个类型进行查看
4. **重置筛选**：点击"全部"按钮显示所有凭证

## 后续优化建议

1. **保存筛选状态**：记住用户的筛选偏好
2. **快捷键支持**：添加键盘快捷键切换类型
3. **高级筛选**：支持按日期、金额等条件组合筛选
4. **导出功能**：支持按类型导出凭证数据
