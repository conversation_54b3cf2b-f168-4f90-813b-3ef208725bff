<script lang="ts" setup>
  import type { CompaniesQueryParams, CompanyData } from '#/api/jsj-ai/types';

  import { computed, onMounted, ref, watch } from 'vue';
  import { useRouter } from 'vue-router';

  import { useUserStore } from '@vben/stores';

  import { SearchOutlined } from '@ant-design/icons-vue';
  import {
    Empty,
    Input,
    message,
    Select,
    Switch,
    Table,
    Tag,
    Tooltip,
  } from 'ant-design-vue';

  import { fetchCompaniesList, updateAutoMode } from '#/api/jsj-ai/api-v2';
  import { useCompanySelection } from '#/hooks/jsj-ai/ai-chat/useCompanySelection';

  const userStore = useUserStore();
  const router = useRouter();
  const {
    fetchCompanyNames,
    handleMonthSelectChange,
    monthOptions,
    selectedCompany,
    selectedMonth,
  } = useCompanySelection();
  const loading = ref(false);
  const companies = ref<CompanyData[]>([]);
  const searchKeyword = ref('');
  const statusFilter = ref<string>('all');

  // 状态筛选选项
  const statusOptions = [
    { label: '全部', value: 'all' },
    { label: '已置顶', value: 'top' },
    { label: '销项发票已完成', value: 'output_invoice_done' },
    { label: '进项普票已完成', value: 'input_general_done' },
    { label: '进项专票已完成', value: 'input_vat_done' },
    { label: '银行回单已完成', value: 'bank_receipt_done' },
    { label: 'AI自动模式', value: 'auto_mode_on' },
  ];

  // 过滤后的公司列表（带预处理的工具提示）
  const filteredCompanies = computed(() => {
    // 添加安全检查，确保 companies.value 存在且为数组
    if (!companies.value || !Array.isArray(companies.value)) {
      return [];
    }

    let result = companies.value;

    // 关键词搜索 - 同时搜索客户名称和客户编号
    if (searchKeyword.value) {
      const keyword = searchKeyword.value.toLowerCase();
      result = result.filter(
        (company) =>
          company.customerName.toLowerCase().includes(keyword) ||
          (company.customerCode &&
            company.customerCode.toLowerCase().includes(keyword)),
      );
    }

    // 状态筛选
    if (statusFilter.value !== 'all') {
      switch (statusFilter.value) {
        case 'auto_mode_on': {
          result = result.filter(
            (company) => company.configs?.auto_mode === 'autojob',
          );
          break;
        }
        case 'bank_receipt_done': {
          result = result.filter(
            (company) => company.bank_receipt?.status === '3',
          );
          break;
        }
        case 'input_general_done': {
          result = result.filter(
            (company) => company.input_invoice_general?.status === '3',
          );
          break;
        }
        case 'input_vat_done': {
          result = result.filter(
            (company) => company.input_invoice_vat?.status === '3',
          );
          break;
        }
        case 'output_invoice_done': {
          result = result.filter(
            (company) => company.output_invoice?.status === '3',
          );
          break;
        }
        case 'top': {
          result = result.filter((company) => company.topFlag);
          break;
        }
      }
    }

    // 预处理工具提示内容，避免在渲染时计算
    return result.map((company) => ({
      ...company,
      _tooltips: {
        bank_receipt: getStatusTooltip(company.bank_receipt?.status || '0'),
        customs_declaration_form: getStatusTooltip(
          company.customs_declaration_form?.status || '0',
        ),
        input_invoice_general: getStatusTooltip(
          company.input_invoice_general?.status || '0',
        ),
        input_invoice_vat: getStatusTooltip(
          company.input_invoice_vat?.status || '0',
        ),
        output_invoice: getStatusTooltip(company.output_invoice?.status || '0'),
        payroll: getStatusTooltip(company.payroll?.status || '0'),
      },
    }));
  });

  // 表格列定义
  const columns = [
    {
      dataIndex: 'customerName',
      ellipsis: true,
      key: 'customerName',
      title: '公司名称',
      width: 200,
    },
    {
      align: 'center' as const,
      key: 'output_invoice',
      title: '销项发票',
      width: 80,
    },
    {
      align: 'center' as const,
      key: 'input_invoice_general',
      title: '进项普票',
      width: 80,
    },
    {
      align: 'center' as const,
      key: 'input_invoice_vat',
      title: '进项专票',
      width: 80,
    },
    {
      align: 'center' as const,
      key: 'bank_receipt',
      title: '银行回单',
      width: 80,
    },
    {
      align: 'center' as const,
      key: 'payroll',
      title: '薪酬',
      width: 80,
    },
    {
      align: 'center' as const,
      key: 'customs_declaration_form',
      title: '报关单',
      width: 80,
    },
    {
      align: 'center' as const,
      key: 'auto_mode',
      title: 'AI自动',
      width: 100,
    },
    {
      align: 'center' as const,
      key: 'action',
      title: '操作',
      width: 120,
    },
  ];

  // 预定义所有状态的工具提示内容（避免运行时计算）
  const STATUS_TOOLTIPS = {
    '0': '❌ 数据未同步\n━━━━━━━━━━━━━━\n✗ 数据未同步\n○ 待生成凭证\n○ 待回写凭证\n\n进度: 0/3 (0%)',
    '1': '� 数据已同步\n━━━━━━━━━━━━━━\n✓ 数据已同步\n○ 待生成凭证\n○ 待回写凭证\n\n进度: 1/3 (33%)',
    '2': '� 凭证已生成\n━━━━━━━━━━━━━━\n✓ 数据已同步\n✓ 凭证已生成\n○ 待回写凭证\n\n进度: 2/3 (67%)',
    '3': '✅ 凭证已回写\n━━━━━━━━━━━━━━\n✓ 数据已同步\n✓ 凭证已生成\n✓ 凭证已回写\n\n进度: 3/3 (100%)',
  } as const;

  // 获取状态工具提示信息（直接查表，无计算）
  const getStatusTooltip = (status: string) => {
    return (
      STATUS_TOOLTIPS[status as keyof typeof STATUS_TOOLTIPS] ||
      `状态: ${status || '未知'}\n请检查数据状态`
    );
  };

  // 统计数据
  const statistics = computed(() => {
    // 添加安全检查，确保 companies.value 存在且为数组
    if (!companies.value || !Array.isArray(companies.value)) {
      return {
        bankReceiptDone: 0,
        inputCommonDone: 0,
        inputVatDone: 0,
        outputVoiceDone: 0,
        topCount: 0,
        total: 0,
      };
    }

    const total = companies.value.length;
    const topCount = companies.value.filter((c) => c.topFlag).length;

    // 根据新的数据结构计算统计
    const outputVoiceDone = companies.value.filter(
      (c) => c.output_invoice?.status === '3',
    ).length;
    const inputCommonDone = companies.value.filter(
      (c) => c.input_invoice_general?.status === '3',
    ).length;
    const inputVatDone = companies.value.filter(
      (c) => c.input_invoice_vat?.status === '3',
    ).length;
    const bankReceiptDone = companies.value.filter(
      (c) => c.bank_receipt?.status === '3',
    ).length;

    return {
      bankReceiptDone,
      inputCommonDone,
      inputVatDone,
      outputVoiceDone,
      topCount,
      total,
    };
  });

  // 获取公司列表数据
  async function fetchCompanies() {
    try {
      loading.value = true;
      const username = userStore.userInfo?.username;
      if (!username) {
        console.error('用户名未找到');
        return;
      }

      const params: CompaniesQueryParams = {
        account_name: username,
        month: selectedMonth.value,
        refresh: 0,
      };

      const response = await fetchCompaniesList(params);
      console.log('response', response);

      companies.value = response;
    } catch (error) {
      console.error('获取公司列表失败:', error);
    } finally {
      loading.value = false;
    }
  }

  // 月份变化时重新获取数据
  const handleMonthChange = () => {
    fetchCompanies();
  };

  // 处理月份选择变化
  const onMonthChange = (value: any) => {
    if (value && typeof value === 'string') {
      handleMonthSelectChange(value, () => {
        fetchCompanies();
      });
    }
  };

  // 处理AI自动模式切换
  const handleAutoModeChange = async (
    record: CompanyData,
    checked: boolean,
  ) => {
    try {
      console.log(
        '切换AI自动模式:',
        record.customerName,
        checked ? '开启' : '关闭',
      );

      // 调用API更新AI自动模式
      const response = await updateAutoMode({
        auto_mode: checked ? 'autojob' : 'close',
        company_name: record.customerName,
      });

      // 更新本地数据
      const company = companies.value.find(
        (c) => c.customerName === record.customerName,
      );
      if (company && company.configs) {
        company.configs.auto_mode = checked ? 'autojob' : 'close';
      }
      console.log('AI自动模式更新成功');
    } catch (error) {
      console.error('更新AI自动模式失败:', error);
      // 这里可以添加错误提示
    }
  };

  // 处理AI工作台按钮点击
  const handleAIWorkspace = async (record: CompanyData) => {
    try {
      if (!record?.customerName) {
        message.error('无效的客户信息');
        return;
      }

      console.log('进入工作台:', record.customerName);

      // 显示加载状态
      message.loading({
        content: '正在切换到工作台模式...',
        key: 'switchMode',
      });

      // 设置当前选择的公司到全局状态
      selectedCompany.value = record.customerName;
      console.log('已设置全局选中公司:', record.customerName);

      // 切换到工作台模式（会自动触发菜单更新）
      const { useMenuModeStore } = await import('#/store/modules/menu-mode');
      const menuModeStore = useMenuModeStore();

      // 切换模式
      await menuModeStore.setMenuMode('workspace');

      // 使用 router.replace 进行导航
      router.replace('/voucher/original');

      // 显示成功提示
      message.success({
        content: `已切换到工作台模式：${record.customerName}`,
        duration: 2,
        key: 'switchMode',
      });
    } catch (error) {
      console.error('切换到工作台模式失败:', error);
      message.error({
        content: '切换到工作台模式失败，请重试',
        duration: 2,
        key: 'switchMode',
      });
    }
  };

  function getProgressInfo(status: string) {
    switch (status) {
      case '0': {
        return { color: 'text-red-500', current: 0, stroke: '#ef4444' };
      }
      case '1': {
        return { color: 'text-blue-500', current: 1, stroke: '#3b82f6' };
      }
      case '2': {
        return { color: 'text-blue-500', current: 2, stroke: '#3b82f6' };
      }
      case '3': {
        return { color: 'text-green-500', current: 3, stroke: '#22c55e' };
      }
      default: {
        return { color: 'text-gray-400', current: 0, stroke: '#a3a3a3' };
      }
    }
  }

  // 监听全局月份状态变化
  watch(
    () => selectedMonth.value,
    () => {
      fetchCompanies();
    },
  );

  onMounted(async () => {
    // 初始化公司列表
    await fetchCompanyNames();
    // 获取公司数据
    fetchCompanies();
  });
</script>

<template>
  <div class="company-status-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-title">
          <h1 class="title">客户管理中心</h1>
          <p class="subtitle">管理和监控所有客户的业务处理状态</p>
        </div>
        <div class="header-stats">
          <div class="stat-item">
            <div class="stat-number">{{ statistics.total }}</div>
            <div class="stat-label">总客户数</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ statistics.topCount }}</div>
            <div class="stat-label">置顶客户</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 状态图例说明 -->
    <div class="status-legend-card">
      <div class="legend-header">
        <div class="legend-title">
          <svg class="legend-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="12" cy="12" r="3"/>
            <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
          </svg>
          <span>进度状态说明</span>
        </div>
      </div>
      <div class="legend-items">
        <div class="legend-item">
          <div class="status-dot status-none"></div>
          <span class="status-text">无业务</span>
        </div>
        <div class="legend-item">
          <div class="status-dot status-pending"></div>
          <span class="status-text">数据未同步</span>
        </div>
        <div class="legend-item">
          <div class="status-dot status-processing"></div>
          <span class="status-text">处理中</span>
        </div>
        <div class="legend-item">
          <div class="status-dot status-completed"></div>
          <span class="status-text">凭证已回写</span>
        </div>
      </div>
    </div>
    <!-- 控制面板 -->
    <div class="control-panel-card">
      <div class="control-panel-header">
        <div class="panel-title">
          <svg class="panel-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M3 6h18M3 12h18M3 18h18"/>
          </svg>
          <span>筛选与搜索</span>
        </div>
      </div>

      <div class="control-panel-content">
        <!-- 月份选择器 -->
        <div class="control-group">
          <label class="control-label">
            <svg class="label-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
              <line x1="16" y1="2" x2="16" y2="6"/>
              <line x1="8" y1="2" x2="8" y2="6"/>
              <line x1="3" y1="10" x2="21" y2="10"/>
            </svg>
            月份
          </label>
          <Select
            :value="selectedMonth"
            placeholder="选择月份"
            size="large"
            :options="monthOptions"
            @change="onMonthChange"
            class="control-select month-select"
          />
        </div>

        <!-- 搜索和筛选 -->
        <div class="search-filter-group">
          <div class="search-group">
            <label class="control-label">
              <svg class="label-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="11" cy="11" r="8"/>
                <path d="m21 21-4.35-4.35"/>
              </svg>
              搜索客户
            </label>
            <Input
              v-model:value="searchKeyword"
              placeholder="请输入客户名称或编号进行搜索"
              size="large"
              allow-clear
              class="search-input"
            >
              <template #prefix>
                <SearchOutlined class="search-prefix-icon" />
              </template>
            </Input>
          </div>

          <div class="filter-group">
            <label class="control-label">
              <svg class="label-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <polygon points="22,3 2,3 10,12.46 10,19 14,21 14,12.46"/>
              </svg>
              状态筛选
            </label>
            <Select
              v-model:value="statusFilter"
              placeholder="选择状态进行筛选"
              size="large"
              :options="statusOptions"
              class="control-select filter-select"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 公司列表表格 -->
    <div class="table-card">
      <div class="table-header">
        <div class="table-title">
          <svg class="table-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M9 11H5a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h4a2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2z"/>
            <path d="M19 11h-4a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h4a2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2z"/>
            <path d="M5 11V7a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v4"/>
            <path d="M15 11V7a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v4"/>
          </svg>
          <span>客户列表</span>
        </div>
        <div class="table-summary">
          <span class="summary-text">共 {{ filteredCompanies.length }} 家客户</span>
        </div>
      </div>

      <div class="table-container">
        <Table
          :columns="columns"
          :data-source="filteredCompanies"
          :loading="loading"
          :pagination="{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            pageSizeOptions: ['10', '20', '50', '100'],
            defaultPageSize: 20,
            size: 'default',
            showLessItems: false,
          }"
          row-key="customerName"
          size="middle"
          class="custom-table"
        >
        <!-- 表格单元格渲染 -->
        <template #bodyCell="{ column, record }">
          <!-- 公司名称列 -->
          <template v-if="column.key === 'customerName'">
            <div class="flex items-center">
              <Tag v-if="record.topFlag" color="orange" class="mr-2">置顶</Tag>
              <Tag
                v-if="record.company_type"
                :color="
                  record.company_type === '小规模纳税人' ? 'blue' : 'green'
                "
                class="mr-2"
              >
                {{ record.company_type === '小规模纳税人' ? '小' : '般' }}
              </Tag>
              <span :title="record.customerName">
                {{ record.customerName }}
              </span>
            </div>
          </template>

          <!-- 业务数据状态列 -->
          <template
            v-else-if="
              [
                'output_invoice',
                'input_invoice_general',
                'input_invoice_vat',
                'bank_receipt',
                'payroll',
                'customs_declaration_form',
              ].includes(column.key as string)
            "
          >
            <div class="flex justify-center">
              <Tooltip placement="top" trigger="hover">
                <template #title>
                  <div
                    class="tooltip-content"
                    v-html="
                      record._tooltips[column.key as string].replace(
                        /\n/g,
                        '<br>',
                      )
                    "
                  ></div>
                </template>
                <span class="inline-block cursor-pointer">
                  <svg class="h-8 w-8" viewBox="0 0 32 32">
                    <!-- 背景圆环 -->
                    <circle
                      cx="16"
                      cy="16"
                      r="14"
                      stroke="#e5e7eb"
                      stroke-width="3"
                      fill="none"
                    />
                    <!-- 进度圆环 -->
                    <circle
                      v-if="
                        getProgressInfo(
                          record[column.key as string]?.status || '0',
                        ).current > 0
                      "
                      cx="16"
                      cy="16"
                      r="14"
                      :stroke="
                        getProgressInfo(
                          record[column.key as string]?.status || '0',
                        ).stroke
                      "
                      stroke-width="3"
                      fill="none"
                      stroke-linecap="round"
                      :stroke-dasharray="`${(getProgressInfo(record[column.key as string]?.status || '0').current / 3) * 87.96}, 87.96`"
                      stroke-dashoffset="0"
                    />
                    <!-- 中心数字 -->
                    <text
                      x="16"
                      y="21"
                      text-anchor="middle"
                      font-size="13"
                      :fill="
                        getProgressInfo(
                          record[column.key as string]?.status || '0',
                        ).stroke
                      "
                    >
                      {{
                        getProgressInfo(
                          record[column.key as string]?.status || '0',
                        ).current
                      }}/3
                    </text>
                  </svg>
                </span>
              </Tooltip>
            </div>
          </template>

          <!-- AI自动模式列 -->
          <template v-else-if="column.key === 'auto_mode'">
            <div class="flex justify-center">
              <Switch
                :checked="record.configs?.auto_mode === 'autojob'"
                size="small"
                @change="
                  (checked) =>
                    handleAutoModeChange(record as CompanyData, !!checked)
                "
              />
            </div>
          </template>

          <!-- 操作列 -->
          <template v-else-if="column.key === 'action'">
            <div class="action-buttons">
              <button
                class="workspace-btn"
                @click="handleAIWorkspace(record as CompanyData)"
              >
                <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
                  <line x1="8" y1="21" x2="16" y2="21"/>
                  <line x1="12" y1="17" x2="12" y2="21"/>
                </svg>
                <span>进入工作台</span>
              </button>
            </div>
          </template>
        </template>

        <!-- 空数据 -->
        <template #emptyText>
          <div class="empty-state">
            <svg class="empty-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="12" cy="12" r="10"/>
              <path d="M8 14s1.5 2 4 2 4-2 4-2"/>
              <line x1="9" y1="9" x2="9.01" y2="9"/>
              <line x1="15" y1="9" x2="15.01" y2="9"/>
            </svg>
            <div class="empty-text">暂无客户数据</div>
            <div class="empty-subtext">请检查筛选条件或联系管理员</div>
          </div>
        </template>
      </Table>
      </div>
    </div>
  </div>
</template>

<style scoped>


  @media (max-width: 768px) {
    .search-filter-group {
      grid-template-columns: 1fr;
    }
  }

  @media (max-width: 768px) {
    .search-filter-group {
      grid-template-columns: 1fr;
    }
  }

  @media (max-width: 768px) {
    .search-filter-group {
      grid-template-columns: 1fr;
    }

    .header-content {
      flex-direction: column;
      gap: 20px;
      text-align: center;
    }

    .header-stats {
      gap: 16px;
    }

    .legend-items {
      gap: 16px;
    }

    .company-status-page {
      padding: 16px;
    }
  }

  /* 页面整体样式 */
  .company-status-page {
    min-height: 100vh;
    padding: 24px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  /* 页面头部样式 */
  .page-header {
    padding: 32px;
    margin-bottom: 24px;
    background: rgb(*********** / 95%);
    backdrop-filter: blur(20px);
    border: 1px solid rgb(*********** / 20%);
    border-radius: 20px;
    box-shadow:
      0 20px 25px -5px rgb(0 0 0 / 10%),
      0 10px 10px -5px rgb(0 0 0 / 4%);
  }

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .header-title .title {
    margin: 0 0 8px;
    font-size: 32px;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .header-title .subtitle {
    margin: 0;
    font-size: 16px;
    font-weight: 400;
    color: #6b7280;
  }

  .header-stats {
    display: flex;
    gap: 32px;
  }

  .stat-item {
    padding: 16px 24px;
    text-align: center;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid rgb(148 163 184 / 20%);
    border-radius: 16px;
  }

  .stat-number {
    font-size: 28px;
    font-weight: 700;
    line-height: 1;
    color: #1e293b;
  }

  .stat-label {
    margin-top: 4px;
    font-size: 14px;
    font-weight: 500;
    color: #64748b;
  }

  /* 状态图例卡片样式 */
  .status-legend-card {
    padding: 24px;
    margin-bottom: 24px;
    background: rgb(*********** / 95%);
    backdrop-filter: blur(20px);
    border: 1px solid rgb(*********** / 20%);
    border-radius: 16px;
    box-shadow:
      0 10px 15px -3px rgb(0 0 0 / 10%),
      0 4px 6px -2px rgb(0 0 0 / 5%);
  }

  .legend-header {
    margin-bottom: 20px;
  }

  .legend-title {
    display: flex;
    gap: 12px;
    align-items: center;
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
  }

  .legend-icon {
    width: 24px;
    height: 24px;
    color: #667eea;
  }

  .legend-items {
    display: flex;
    flex-wrap: wrap;
    gap: 32px;
  }

  .legend-item {
    display: flex;
    gap: 8px;
    align-items: center;
    padding: 8px 16px;
    background: rgb(248 250 252 / 80%);
    border: 1px solid rgb(226 232 240 / 60%);
    border-radius: 12px;
  }

  .status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
  }

  .status-none { background: #9ca3af; }

  .status-pending { background: #ef4444; }

  .status-processing { background: #3b82f6; }

  .status-completed { background: #10b981; }

  .status-text {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
  }

  /* 控制面板样式 */
  .control-panel-card {
    padding: 24px;
    margin-bottom: 24px;
    background: rgb(*********** / 95%);
    backdrop-filter: blur(20px);
    border: 1px solid rgb(*********** / 20%);
    border-radius: 16px;
    box-shadow:
      0 10px 15px -3px rgb(0 0 0 / 10%),
      0 4px 6px -2px rgb(0 0 0 / 5%);
  }

  .control-panel-header {
    margin-bottom: 24px;
  }

  .panel-title {
    display: flex;
    gap: 12px;
    align-items: center;
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
  }

  .panel-icon {
    width: 24px;
    height: 24px;
    color: #667eea;
  }

  .control-panel-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .control-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .search-filter-group {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 24px;
  }

  .search-group,
  .filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .control-label {
    display: flex;
    gap: 8px;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
  }

  .label-icon {
    width: 16px;
    height: 16px;
    color: #6b7280;
  }

  .control-select {
    border: 2px solid #e5e7eb !important;
    border-radius: 12px !important;
    transition: all 0.2s ease !important;
  }

  .month-select {
    width: 180px;
  }

  .filter-select {
    width: 100%;
  }

  :deep(.control-select .ant-select-selector) {
    height: auto !important;
    min-height: 44px !important;
    padding: 8px 16px !important;
    background: rgb(249 250 251 / 80%) !important;
    border: none !important;
    border-radius: 12px !important;
    box-shadow: none !important;
  }

  :deep(.control-select:hover .ant-select-selector) {
    background: rgb(*********** / 90%) !important;
    border-color: #667eea !important;
  }

  :deep(.control-select.ant-select-focused .ant-select-selector) {
    background: rgb(*********** / 100%) !important;
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgb(102 126 234 / 10%) !important;
  }

  .search-input {
    border: 2px solid #e5e7eb !important;
    border-radius: 12px !important;
    transition: all 0.2s ease !important;
  }

  :deep(.search-input .ant-input) {
    padding: 12px 16px !important;
    font-size: 14px !important;
    background: rgb(249 250 251 / 80%) !important;
    border: none !important;
    border-radius: 12px !important;
    box-shadow: none !important;
  }

  :deep(.search-input:hover .ant-input) {
    background: rgb(*********** / 90%) !important;
  }

  :deep(.search-input.ant-input-affix-wrapper-focused .ant-input) {
    background: rgb(*********** / 100%) !important;
  }

  :deep(.search-input.ant-input-affix-wrapper-focused) {
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgb(102 126 234 / 10%) !important;
  }

  .search-prefix-icon {
    font-size: 16px !important;
    color: #9ca3af !important;
  }

  /* 表格卡片样式 */
  .table-card {
    overflow: hidden;
    background: rgb(*********** / 95%);
    backdrop-filter: blur(20px);
    border: 1px solid rgb(*********** / 20%);
    border-radius: 16px;
    box-shadow:
      0 10px 15px -3px rgb(0 0 0 / 10%),
      0 4px 6px -2px rgb(0 0 0 / 5%);
  }

  .table-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid rgb(226 232 240 / 60%);
  }

  .table-title {
    display: flex;
    gap: 12px;
    align-items: center;
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
  }

  .table-icon {
    width: 24px;
    height: 24px;
    color: #667eea;
  }

  .table-summary {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .summary-text {
    font-size: 14px;
    font-weight: 500;
    color: #64748b;
  }

  .table-container {
    padding: 0;
  }

  /* 自定义表格样式 */
  :deep(.custom-table) {
    background: transparent;
  }

  :deep(.custom-table .ant-table) {
    background: transparent;
  }

  :deep(.custom-table .ant-table-thead > tr > th) {
    padding: 16px 12px;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    text-align: center;
    background: rgb(248 250 252 / 80%);
    border-bottom: 2px solid #e2e8f0;
  }

  :deep(.custom-table .ant-table-tbody > tr > td) {
    padding: 12px;
    background: rgb(*********** / 60%);
    border-bottom: 1px solid rgb(226 232 240 / 50%);
    transition: all 0.2s ease;
  }

  :deep(.custom-table .ant-table-tbody > tr:hover > td) {
    background: rgb(102 126 234 / 5%);
  }

  :deep(.custom-table .ant-table-tbody > tr:nth-child(even) > td) {
    background: rgb(248 250 252 / 40%);
  }

  :deep(.custom-table .ant-table-tbody > tr:nth-child(even):hover > td) {
    background: rgb(102 126 234 / 8%);
  }

  /* 操作按钮样式 */
  .action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;
  }

  .workspace-btn {
    display: flex;
    gap: 6px;
    align-items: center;
    padding: 8px 16px;
    font-size: 12px;
    font-weight: 500;
    color: white;
    cursor: pointer;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgb(102 126 234 / 20%);
    transition: all 0.2s ease;
  }

  .workspace-btn:hover {
    box-shadow: 0 4px 8px rgb(102 126 234 / 30%);
    transform: translateY(-1px);
  }

  .workspace-btn:active {
    transform: translateY(0);
  }

  .btn-icon {
    width: 14px;
    height: 14px;
  }

  /* 空状态样式 */
  .empty-state {
    display: flex;
    flex-direction: column;
    gap: 16px;
    align-items: center;
    padding: 48px 24px;
    color: #6b7280;
  }

  .empty-icon {
    width: 64px;
    height: 64px;
    color: #d1d5db;
  }

  .empty-text {
    font-size: 18px;
    font-weight: 600;
    color: #374151;
  }

  .empty-subtext {
    font-size: 14px;
    color: #6b7280;
  }

  /* 优化 Tooltip 样式 */
  :deep(.ant-tooltip) {
    z-index: 1000;
    transition:
      opacity 0.2s ease-in-out,
      transform 0.2s ease-in-out;
  }

  :deep(.ant-tooltip-inner) {
    position: relative;
    max-width: 280px;
    padding: 16px 20px;
    font-family:
      -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue',
      Arial, sans-serif;
    font-size: 13px;
    font-weight: 400;
    line-height: 1.6;
    color: #f9fafb;
    letter-spacing: 0.025em;
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    border: 1px solid #4b5563;
    border-radius: 10px;
    box-shadow:
      0 10px 25px -5px rgb(0 0 0 / 10%),
      0 10px 10px -5px rgb(0 0 0 / 4%);
  }

  :deep(.tooltip-content) {
    word-break: break-word;
    white-space: normal;
  }

  :deep(.tooltip-content br) {
    display: block;
    margin: 4px 0;
  }

  :deep(.ant-tooltip-arrow::before) {
    background-color: #1f2937;
  }

  :deep(svg) {
    pointer-events: auto;
  }

  /* 分页样式优化 */
  :deep(.ant-pagination) {
    padding: 0 24px 24px;
    margin: 24px 0 0;
  }

  :deep(.ant-pagination .ant-pagination-item) {
    background: rgb(*********** / 80%);
    border: 1px solid #e5e7eb;
    border-radius: 8px;
  }

  :deep(.ant-pagination .ant-pagination-item:hover) {
    background: rgb(102 126 234 / 10%);
    border-color: #667eea;
  }

  :deep(.ant-pagination .ant-pagination-item-active) {
    background: #667eea;
    border-color: #667eea;
  }

  :deep(.ant-pagination .ant-pagination-item-active a) {
    color: white;
  }
</style>
