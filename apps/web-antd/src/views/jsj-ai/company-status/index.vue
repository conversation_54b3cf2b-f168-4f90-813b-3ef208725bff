<script lang="ts" setup>
  import type { CompaniesQueryParams, CompanyData } from '#/api/jsj-ai/types';

  import { computed, onMounted, ref, watch } from 'vue';
  import { useRouter } from 'vue-router';

  import { useUserStore } from '@vben/stores';

  import { SearchOutlined } from '@ant-design/icons-vue';
  import {
    Pagination as APagination,
    Input,
    message,
    Select,
    Switch,
    Table,
    Tag,
    Tooltip,
  } from 'ant-design-vue';

  import { fetchCompaniesList, updateAutoMode } from '#/api/jsj-ai/api-v2';
  import { useCompanySelection } from '#/hooks/jsj-ai/ai-chat/useCompanySelection';

  const userStore = useUserStore();
  const router = useRouter();
  const {
    fetchCompanyNames,
    handleMonthSelectChange,
    monthOptions,
    selectedCompany,
    selectedMonth,
  } = useCompanySelection();
  const loading = ref(false);
  const companies = ref<CompanyData[]>([]);
  const searchKeyword = ref('');
  const statusFilter = ref<string>('all');

  // 分页相关
  const currentPage = ref(1);
  const pageSize = ref(20);

  // 状态筛选选项
  const statusOptions = [
    { label: '全部', value: 'all' },
    { label: '已置顶', value: 'top' },
    { label: '销项发票已完成', value: 'output_invoice_done' },
    { label: '进项普票已完成', value: 'input_general_done' },
    { label: '进项专票已完成', value: 'input_vat_done' },
    { label: '银行回单已完成', value: 'bank_receipt_done' },
    { label: 'AI自动模式', value: 'auto_mode_on' },
  ];

  // 过滤后的公司列表（带预处理的工具提示）
  const allFilteredCompanies = computed(() => {
    // 添加安全检查，确保 companies.value 存在且为数组
    if (!companies.value || !Array.isArray(companies.value)) {
      return [];
    }

    let result = companies.value;

    // 关键词搜索 - 同时搜索客户名称和客户编号
    if (searchKeyword.value) {
      const keyword = searchKeyword.value.toLowerCase();
      result = result.filter(
        (company) =>
          company.customerName.toLowerCase().includes(keyword) ||
          (company.customerCode &&
            company.customerCode.toLowerCase().includes(keyword)),
      );
    }

    // 状态筛选
    if (statusFilter.value !== 'all') {
      switch (statusFilter.value) {
        case 'auto_mode_on': {
          result = result.filter(
            (company) => company.configs?.auto_mode === 'autojob',
          );
          break;
        }
        case 'bank_receipt_done': {
          result = result.filter(
            (company) => company.bank_receipt?.status === '3',
          );
          break;
        }
        case 'input_general_done': {
          result = result.filter(
            (company) => company.input_invoice_general?.status === '3',
          );
          break;
        }
        case 'input_vat_done': {
          result = result.filter(
            (company) => company.input_invoice_vat?.status === '3',
          );
          break;
        }
        case 'output_invoice_done': {
          result = result.filter(
            (company) => company.output_invoice?.status === '3',
          );
          break;
        }
        case 'top': {
          result = result.filter((company) => company.topFlag);
          break;
        }
      }
    }

    // 预处理工具提示内容，避免在渲染时计算
    return result.map((company) => ({
      ...company,
      _tooltips: {
        bank_receipt: getStatusTooltip(company.bank_receipt?.status || '0'),
        customs_declaration_form: getStatusTooltip(
          company.customs_declaration_form?.status || '0',
        ),
        input_invoice_general: getStatusTooltip(
          company.input_invoice_general?.status || '0',
        ),
        input_invoice_vat: getStatusTooltip(
          company.input_invoice_vat?.status || '0',
        ),
        output_invoice: getStatusTooltip(company.output_invoice?.status || '0'),
        payroll: getStatusTooltip(company.payroll?.status || '0'),
      },
    }));
  });

  // 当前页显示的公司列表
  const filteredCompanies = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value;
    const end = start + pageSize.value;
    return allFilteredCompanies.value.slice(start, end);
  });

  // 表格列定义
  const columns = [
    {
      dataIndex: 'customerName',
      ellipsis: true,
      key: 'customerName',
      title: '公司名称',
      width: 200,
    },
    {
      align: 'center' as const,
      key: 'output_invoice',
      title: '销项发票',
      width: 80,
    },
    {
      align: 'center' as const,
      key: 'input_invoice_general',
      title: '进项普票',
      width: 80,
    },
    {
      align: 'center' as const,
      key: 'input_invoice_vat',
      title: '进项专票',
      width: 80,
    },
    {
      align: 'center' as const,
      key: 'bank_receipt',
      title: '银行回单',
      width: 80,
    },
    {
      align: 'center' as const,
      key: 'payroll',
      title: '薪酬',
      width: 80,
    },
    {
      align: 'center' as const,
      key: 'customs_declaration_form',
      title: '报关单',
      width: 80,
    },
    {
      align: 'center' as const,
      key: 'auto_mode',
      title: 'AI自动',
      width: 100,
    },
    {
      align: 'center' as const,
      key: 'action',
      title: '操作',
      width: 120,
    },
  ];

  // 预定义所有状态的工具提示内容（避免运行时计算）
  const STATUS_TOOLTIPS = {
    '0': '❌ 数据未同步\n━━━━━━━━━━━━━━\n✗ 数据未同步\n○ 待生成凭证\n○ 待回写凭证\n\n进度: 0/3 (0%)',
    '1': '� 数据已同步\n━━━━━━━━━━━━━━\n✓ 数据已同步\n○ 待生成凭证\n○ 待回写凭证\n\n进度: 1/3 (33%)',
    '2': '� 凭证已生成\n━━━━━━━━━━━━━━\n✓ 数据已同步\n✓ 凭证已生成\n○ 待回写凭证\n\n进度: 2/3 (67%)',
    '3': '✅ 凭证已回写\n━━━━━━━━━━━━━━\n✓ 数据已同步\n✓ 凭证已生成\n✓ 凭证已回写\n\n进度: 3/3 (100%)',
  } as const;

  // 获取状态工具提示信息（直接查表，无计算）
  const getStatusTooltip = (status: string) => {
    return (
      STATUS_TOOLTIPS[status as keyof typeof STATUS_TOOLTIPS] ||
      `状态: ${status || '未知'}\n请检查数据状态`
    );
  };

  // 统计数据
  const statistics = computed(() => {
    // 添加安全检查，确保 companies.value 存在且为数组
    if (!companies.value || !Array.isArray(companies.value)) {
      return {
        bankReceiptDone: 0,
        inputCommonDone: 0,
        inputVatDone: 0,
        outputVoiceDone: 0,
        topCount: 0,
        total: 0,
      };
    }

    const total = allFilteredCompanies.value.length;
    const topCount = allFilteredCompanies.value.filter((c) => c.topFlag).length;

    // 根据新的数据结构计算统计
    const outputVoiceDone = allFilteredCompanies.value.filter(
      (c) => c.output_invoice?.status === '3',
    ).length;
    const inputCommonDone = allFilteredCompanies.value.filter(
      (c) => c.input_invoice_general?.status === '3',
    ).length;
    const inputVatDone = allFilteredCompanies.value.filter(
      (c) => c.input_invoice_vat?.status === '3',
    ).length;
    const bankReceiptDone = allFilteredCompanies.value.filter(
      (c) => c.bank_receipt?.status === '3',
    ).length;

    return {
      bankReceiptDone,
      inputCommonDone,
      inputVatDone,
      outputVoiceDone,
      topCount,
      total,
    };
  });

  // 获取公司列表数据
  async function fetchCompanies() {
    try {
      loading.value = true;
      const username = userStore.userInfo?.username;
      if (!username) {
        console.error('用户名未找到');
        return;
      }

      const params: CompaniesQueryParams = {
        account_name: username,
        month: selectedMonth.value,
        refresh: 0,
      };

      const response = await fetchCompaniesList(params);
      console.log('response', response);

      companies.value = response;
    } catch (error) {
      console.error('获取公司列表失败:', error);
    } finally {
      loading.value = false;
    }
  }

  // 月份变化时重新获取数据
  const handleMonthChange = () => {
    fetchCompanies();
  };

  // 处理月份选择变化
  const onMonthChange = (value: any) => {
    if (value && typeof value === 'string') {
      handleMonthSelectChange(value, () => {
        fetchCompanies();
      });
    }
  };

  // 处理AI自动模式切换
  const handleAutoModeChange = async (
    record: CompanyData,
    checked: boolean,
  ) => {
    try {
      console.log(
        '切换AI自动模式:',
        record.customerName,
        checked ? '开启' : '关闭',
      );

      // 调用API更新AI自动模式
      const response = await updateAutoMode({
        auto_mode: checked ? 'autojob' : 'close',
        company_name: record.customerName,
      });

      // 更新本地数据
      const company = companies.value.find(
        (c) => c.customerName === record.customerName,
      );
      if (company && company.configs) {
        company.configs.auto_mode = checked ? 'autojob' : 'close';
      }
      console.log('AI自动模式更新成功');
    } catch (error) {
      console.error('更新AI自动模式失败:', error);
      // 这里可以添加错误提示
    }
  };

  // 处理AI工作台按钮点击
  const handleAIWorkspace = async (record: CompanyData) => {
    try {
      if (!record?.customerName) {
        message.error('无效的客户信息');
        return;
      }

      console.log('进入工作台:', record.customerName);

      // 显示加载状态
      message.loading({
        content: '正在切换到工作台模式...',
        key: 'switchMode',
      });

      // 设置当前选择的公司到全局状态
      selectedCompany.value = record.customerName;
      console.log('已设置全局选中公司:', record.customerName);

      // 切换到工作台模式（会自动触发菜单更新）
      const { useMenuModeStore } = await import('#/store/modules/menu-mode');
      const menuModeStore = useMenuModeStore();

      // 切换模式
      await menuModeStore.setMenuMode('workspace');

      // 使用 router.replace 进行导航
      router.replace('/voucher/original');

      // 显示成功提示
      message.success({
        content: `已切换到工作台模式：${record.customerName}`,
        duration: 2,
        key: 'switchMode',
      });
    } catch (error) {
      console.error('切换到工作台模式失败:', error);
      message.error({
        content: '切换到工作台模式失败，请重试',
        duration: 2,
        key: 'switchMode',
      });
    }
  };

  function getProgressInfo(status: string) {
    switch (status) {
      case '0': {
        return { color: 'text-red-500', current: 0, stroke: '#ef4444' };
      }
      case '1': {
        return { color: 'text-blue-500', current: 1, stroke: '#3b82f6' };
      }
      case '2': {
        return { color: 'text-blue-500', current: 2, stroke: '#3b82f6' };
      }
      case '3': {
        return { color: 'text-green-500', current: 3, stroke: '#22c55e' };
      }
      default: {
        return { color: 'text-gray-400', current: 0, stroke: '#a3a3a3' };
      }
    }
  }

  // 监听全局月份状态变化
  watch(
    () => selectedMonth.value,
    () => {
      fetchCompanies();
    },
  );

  // 监听搜索和筛选变化，重置分页
  watch([searchKeyword, statusFilter], () => {
    currentPage.value = 1;
  });

  onMounted(async () => {
    // 初始化公司列表
    await fetchCompanyNames();
    // 获取公司数据
    fetchCompanies();
  });
</script>

<template>
  <div class="company-status-page">
    <!-- 页面头部 - 紧凑版 -->
    <div class="page-header-compact">
      <div class="header-left">
        <h2 class="page-title">客户状态</h2>
        <div class="status-legend">
          <div class="legend-item">
            <div class="status-dot status-none"></div>
            <span>无业务</span>
          </div>
          <div class="legend-item">
            <div class="status-dot status-pending"></div>
            <span>未同步</span>
          </div>
          <div class="legend-item">
            <div class="status-dot status-processing"></div>
            <span>处理中</span>
          </div>
          <div class="legend-item">
            <div class="status-dot status-completed"></div>
            <span>已完成</span>
          </div>
        </div>
      </div>
      <div class="header-stats-compact">
        <span class="stat-text">共 {{ statistics.total }} 家客户</span>
        <span class="stat-text">置顶 {{ statistics.topCount }} 家</span>
      </div>
    </div>
    <!-- 控制面板 - 紧凑版 -->
    <div class="control-panel-compact">
      <div class="control-row">
        <div class="control-item">
          <span class="control-label-compact">月份</span>
          <Select
            :value="selectedMonth"
            placeholder="选择月份"
            size="middle"
            :options="monthOptions"
            @change="onMonthChange"
            class="control-select-compact"
          />
        </div>

        <div class="control-item search-item">
          <Input
            v-model:value="searchKeyword"
            placeholder="搜索客户名称或编号"
            size="middle"
            allow-clear
            class="search-input-compact"
          >
            <template #prefix>
              <SearchOutlined class="search-icon" />
            </template>
          </Input>
        </div>

        <div class="control-item">
          <span class="control-label-compact">筛选</span>
          <Select
            v-model:value="statusFilter"
            placeholder="状态筛选"
            size="middle"
            :options="statusOptions"
            class="control-select-compact"
          />
        </div>
      </div>
    </div>

    <!-- 公司列表表格 - 紧凑版 -->
    <div class="table-card-compact">
      <div class="table-wrapper">
        <Table
          :columns="columns"
          :data-source="filteredCompanies"
          :loading="loading"
          :pagination="false"
          row-key="customerName"
          size="middle"
          class="custom-table"
          :scroll="{ y: 'calc(100vh - 280px)' }"
        >
          <!-- 表格单元格渲染 -->
          <template #bodyCell="{ column, record }">
            <!-- 公司名称列 -->
            <template v-if="column.key === 'customerName'">
              <div class="flex items-center">
                <Tag v-if="record.topFlag" color="orange" class="mr-2">
                  置顶
                </Tag>
                <Tag
                  v-if="record.company_type"
                  :color="
                    record.company_type === '小规模纳税人' ? 'blue' : 'green'
                  "
                  class="mr-2"
                >
                  {{ record.company_type === '小规模纳税人' ? '小' : '般' }}
                </Tag>
                <span :title="record.customerName">
                  {{ record.customerName }}
                </span>
              </div>
            </template>

            <!-- 业务数据状态列 -->
            <template
              v-else-if="
                [
                  'output_invoice',
                  'input_invoice_general',
                  'input_invoice_vat',
                  'bank_receipt',
                  'payroll',
                  'customs_declaration_form',
                ].includes(column.key as string)
              "
            >
              <div class="flex justify-center">
                <Tooltip placement="top" trigger="hover">
                  <template #title>
                    <div
                      class="tooltip-content"
                      v-html="
                        record._tooltips[column.key as string].replace(
                          /\n/g,
                          '<br>',
                        )
                      "
                    ></div>
                  </template>
                  <span class="inline-block cursor-pointer">
                    <svg class="h-8 w-8" viewBox="0 0 32 32">
                      <!-- 背景圆环 -->
                      <circle
                        cx="16"
                        cy="16"
                        r="14"
                        stroke="#e5e7eb"
                        stroke-width="3"
                        fill="none"
                      />
                      <!-- 进度圆环 -->
                      <circle
                        v-if="
                          getProgressInfo(
                            record[column.key as string]?.status || '0',
                          ).current > 0
                        "
                        cx="16"
                        cy="16"
                        r="14"
                        :stroke="
                          getProgressInfo(
                            record[column.key as string]?.status || '0',
                          ).stroke
                        "
                        stroke-width="3"
                        fill="none"
                        stroke-linecap="round"
                        :stroke-dasharray="`${(getProgressInfo(record[column.key as string]?.status || '0').current / 3) * 87.96}, 87.96`"
                        stroke-dashoffset="0"
                      />
                      <!-- 中心数字 -->
                      <text
                        x="16"
                        y="21"
                        text-anchor="middle"
                        font-size="13"
                        :fill="
                          getProgressInfo(
                            record[column.key as string]?.status || '0',
                          ).stroke
                        "
                      >
                        {{
                          getProgressInfo(
                            record[column.key as string]?.status || '0',
                          ).current
                        }}/3
                      </text>
                    </svg>
                  </span>
                </Tooltip>
              </div>
            </template>

            <!-- AI自动模式列 -->
            <template v-else-if="column.key === 'auto_mode'">
              <div class="flex justify-center">
                <Switch
                  :checked="record.configs?.auto_mode === 'autojob'"
                  size="small"
                  @change="
                    (checked) =>
                      handleAutoModeChange(record as CompanyData, !!checked)
                  "
                />
              </div>
            </template>

            <!-- 操作列 -->
            <template v-else-if="column.key === 'action'">
              <div class="action-buttons">
                <button
                  class="workspace-btn"
                  @click="handleAIWorkspace(record as CompanyData)"
                >
                  <svg
                    class="btn-icon"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                  >
                    <rect x="2" y="3" width="20" height="14" rx="2" ry="2" />
                    <line x1="8" y1="21" x2="16" y2="21" />
                    <line x1="12" y1="17" x2="12" y2="21" />
                  </svg>
                  <span>进入工作台</span>
                </button>
              </div>
            </template>
          </template>

          <!-- 空数据 -->
          <template #emptyText>
            <div class="empty-state">
              <svg
                class="empty-icon"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
              >
                <circle cx="12" cy="12" r="10" />
                <path d="M8 14s1.5 2 4 2 4-2 4-2" />
                <line x1="9" y1="9" x2="9.01" y2="9" />
                <line x1="15" y1="9" x2="15.01" y2="9" />
              </svg>
              <div class="empty-text">暂无客户数据</div>
              <div class="empty-subtext">请检查筛选条件或联系管理员</div>
            </div>
          </template>
        </Table>
      </div>

      <!-- 固定分页器 -->
      <div class="table-pagination-fixed">
        <APagination
          v-model:current="currentPage"
          v-model:page-size="pageSize"
          :total="allFilteredCompanies.length"
          :show-size-changer="true"
          :show-quick-jumper="true"
          :show-total="
            (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
          "
          :page-size-options="['10', '20', '50', '100']"
          size="default"
          :show-less-items="false"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
  /* 页面整体样式 - 紧凑版 */
  .company-status-page {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: calc(100vh - 88px);
    padding: 16px;
    overflow: hidden;
    font-family:
      -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue',
      Arial, sans-serif;
    background: #f8fafc;
  }

  /* 页面头部样式 - 紧凑版 */
  .page-header-compact {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    margin-bottom: 12px;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgb(0 0 0 / 10%);
  }

  .header-left {
    display: flex;
    gap: 24px;
    align-items: center;
  }

  .page-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
  }

  .status-legend {
    display: flex;
    gap: 16px;
    align-items: center;
  }

  .legend-item {
    display: flex;
    gap: 6px;
    align-items: center;
  }

  .legend-item span {
    font-size: 12px;
    font-weight: 500;
    color: #6b7280;
  }

  .status-dot {
    flex-shrink: 0;
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }

  .status-none {
    background: #9ca3af;
  }

  .status-pending {
    background: #ef4444;
  }

  .status-processing {
    background: #3b82f6;
  }

  .status-completed {
    background: #10b981;
  }

  .header-stats-compact {
    display: flex;
    gap: 16px;
    align-items: center;
  }

  .stat-text {
    font-size: 13px;
    font-weight: 500;
    color: #6b7280;
  }

  /* 控制面板样式 - 紧凑版 */
  .control-panel-compact {
    flex-shrink: 0;
    padding: 12px 16px;
    margin-bottom: 12px;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgb(0 0 0 / 10%);
  }

  .control-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: center;
  }

  .control-item {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .search-item {
    flex: 1;
    min-width: 300px;
  }

  .control-label-compact {
    font-size: 13px;
    font-weight: 500;
    color: #6b7280;
    white-space: nowrap;
  }

  .control-select-compact {
    width: 140px;
  }

  :deep(.control-select-compact .ant-select-selector) {
    height: 32px !important;
    padding: 0 8px !important;
    background: #f9fafb !important;
    border: 1px solid #d1d5db !important;
    border-radius: 6px !important;
  }

  :deep(.control-select-compact:hover .ant-select-selector) {
    border-color: #9ca3af !important;
  }

  :deep(.control-select-compact.ant-select-focused .ant-select-selector) {
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 2px rgb(59 130 246 / 10%) !important;
  }

  .search-input-compact {
    flex: 1;
  }

  :deep(.search-input-compact .ant-input) {
    height: 32px !important;
    padding: 0 8px 0 32px !important;
    background: #f9fafb !important;
    border: 1px solid #d1d5db !important;
    border-radius: 6px !important;
  }

  :deep(.search-input-compact:hover .ant-input) {
    border-color: #9ca3af !important;
  }

  :deep(.search-input-compact.ant-input-affix-wrapper-focused) {
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 2px rgb(59 130 246 / 10%) !important;
  }

  .search-icon {
    font-size: 14px !important;
    color: #9ca3af !important;
  }

  /* 表格卡片样式 - 紧凑版 */
  .table-card-compact {
    display: flex;
    flex: 1;
    flex-direction: column;
    min-height: 0;
    overflow: hidden;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgb(0 0 0 / 10%);
  }

  .table-wrapper {
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: hidden;
  }

  .table-pagination-fixed {
    display: flex;
    flex-shrink: 0;
    justify-content: center;
    padding: 8px 12px;
    background: #fafafa;
    border-top: 1px solid #f0f0f0;
  }

  /* 自定义表格样式 - 紧凑版 */
  :deep(.table-wrapper .ant-table) {
    height: 100%;
    background: transparent;
  }

  :deep(.table-wrapper .ant-table-container) {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  :deep(.table-wrapper .ant-table-header) {
    flex-shrink: 0;
    overflow: hidden !important;
  }

  :deep(.table-wrapper .ant-table-body) {
    flex: 1;
    min-height: 0;
    max-height: calc(100vh - 280px);
    padding-bottom: 8px;
    overflow-x: hidden;
    overflow-y: auto !important;
  }

  :deep(.table-wrapper .ant-table-tbody) {
    min-height: 100%;
  }

  /* 确保表格内容可以滚动 */
  :deep(.table-wrapper .ant-table-content) {
    height: 100%;
    overflow: hidden;
  }

  :deep(.table-wrapper .ant-table-thead > tr > th) {
    position: sticky;
    top: 0;
    z-index: 10;
    padding: 12px 8px;
    font-size: 13px;
    font-weight: 600;
    color: #374151;
    text-align: center;
    background: #f9fafb !important;
    border-bottom: 1px solid #e5e7eb;
  }

  :deep(.table-wrapper .ant-table-tbody > tr > td) {
    padding: 8px;
    background: white;
    border-bottom: 1px solid #f3f4f6;
    transition: all 0.15s ease;
  }

  :deep(.table-wrapper .ant-table-tbody > tr:hover > td) {
    background: #f8fafc;
  }

  :deep(.table-wrapper .ant-table-tbody > tr:nth-child(even) > td) {
    background: #fafbfc;
  }

  :deep(.table-wrapper .ant-table-tbody > tr:nth-child(even):hover > td) {
    background: #f1f5f9;
  }

  /* 操作按钮样式 - 紧凑版 */
  .action-buttons {
    display: flex;
    gap: 4px;
    justify-content: center;
  }

  .workspace-btn {
    display: flex;
    gap: 4px;
    align-items: center;
    padding: 4px 12px;
    font-size: 12px;
    font-weight: 500;
    color: white;
    cursor: pointer;
    background: #3b82f6;
    border: none;
    border-radius: 4px;
    transition: all 0.15s ease;
  }

  .workspace-btn:hover {
    background: #2563eb;
    transform: translateY(-1px);
  }

  .workspace-btn:active {
    transform: translateY(0);
  }

  .btn-icon {
    width: 12px;
    height: 12px;
  }

  /* 空状态样式 */
  .empty-state {
    display: flex;
    flex-direction: column;
    gap: 16px;
    align-items: center;
    padding: 48px 24px;
    color: #6b7280;
  }

  .empty-icon {
    width: 64px;
    height: 64px;
    color: #d1d5db;
  }

  .empty-text {
    font-size: 18px;
    font-weight: 600;
    color: #374151;
  }

  .empty-subtext {
    font-size: 14px;
    color: #6b7280;
  }

  /* 优化 Tooltip 样式 */
  :deep(.ant-tooltip) {
    z-index: 1000;
    transition:
      opacity 0.2s ease-in-out,
      transform 0.2s ease-in-out;
  }

  :deep(.ant-tooltip-inner) {
    position: relative;
    max-width: 280px;
    padding: 16px 20px;
    font-family:
      -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue',
      Arial, sans-serif;
    font-size: 13px;
    font-weight: 400;
    line-height: 1.6;
    color: #f9fafb;
    letter-spacing: 0.025em;
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    border: 1px solid #4b5563;
    border-radius: 10px;
    box-shadow:
      0 10px 25px -5px rgb(0 0 0 / 10%),
      0 10px 10px -5px rgb(0 0 0 / 4%);
  }

  :deep(.tooltip-content) {
    word-break: break-word;
    white-space: normal;
  }

  :deep(.tooltip-content br) {
    display: block;
    margin: 4px 0;
  }

  :deep(.ant-tooltip-arrow::before) {
    background-color: #1f2937;
  }

  :deep(svg) {
    pointer-events: auto;
  }

  /* 分页样式优化 */
  :deep(.ant-pagination) {
    padding: 0 24px 24px;
    margin: 24px 0 0;
  }

  :deep(.ant-pagination .ant-pagination-item) {
    background: rgb(255 255 255 / 80%);
    border: 1px solid #e5e7eb;
    border-radius: 8px;
  }

  :deep(.ant-pagination .ant-pagination-item:hover) {
    background: rgb(102 126 234 / 10%);
    border-color: #667eea;
  }

  :deep(.ant-pagination .ant-pagination-item-active) {
    background: #667eea;
    border-color: #667eea;
  }

  :deep(.ant-pagination .ant-pagination-item-active a) {
    color: white;
  }
</style>
